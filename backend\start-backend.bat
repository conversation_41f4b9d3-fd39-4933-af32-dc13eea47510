@echo off
echo ========================================
echo Starting Promptify Backend Server
echo ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Display Node.js version
echo Node.js version:
node --version
echo.

:: Check if package.json exists
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Please run this script from the backend directory
    pause
    exit /b 1
)

:: Check if node_modules exists, if not install dependencies
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
    echo.
)

:: Check if .env file exists
if not exist ".env" (
    echo WARNING: .env file not found
    echo Creating .env from .env.example...
    if exist ".env.example" (
        copy ".env.example" ".env"
        echo Please configure your .env file with proper values
    ) else (
        echo ERROR: .env.example file not found
        pause
        exit /b 1
    )
    echo.
)

:: Display configuration
echo Configuration:
echo - Port: 8001
echo - Environment: development
echo - Frontend URL: http://localhost:8080
echo.

:: Run startup validation
echo Running startup validation...
npm run validate
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Startup validation failed
    echo Please fix the issues above before starting the server
    pause
    exit /b 1
)

:: Start the server
echo.
echo Starting backend server...
echo Press Ctrl+C to stop the server
echo.
npm run dev

:: If the server stops, pause to show any error messages
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Server failed to start
    echo Check the error messages above
    pause
)