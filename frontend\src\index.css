@tailwind base;
@tailwind components;
@tailwind utilities;

/* Promptify Design System - Modern AI-Powered Prompt Platform */
@layer base {
  :root {
    /* Light mode colors */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    
    /* AI-inspired purple-blue primary */
    --primary: 263 70% 50%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 263 70% 65%;
    
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 45%;
    
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    
    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 263 70% 50%;
    
    --radius: 1rem;
    
    /* AI-themed gradients */
    --gradient-primary: linear-gradient(135deg, hsl(263 70% 50%), hsl(280 70% 60%));
    --gradient-secondary: linear-gradient(135deg, hsl(240 4.8% 95.9%), hsl(240 5.9% 90%));
    --gradient-ai: linear-gradient(135deg, hsl(263 70% 50%), hsl(280 70% 60%), hsl(300 70% 65%));
    
    /* Shadows with AI glow effect */
    --shadow-ai: 0 10px 40px -10px hsl(263 70% 50% / 0.3);
    --shadow-card: 0 4px 20px -4px hsl(240 10% 3.9% / 0.1);
    --shadow-glow: 0 0 40px hsl(263 70% 65% / 0.4);
    
    /* Animations */
    --transition-ai: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark {
    /* Dark mode - AI-inspired dark theme */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    
    --card: 240 10% 6%;
    --card-foreground: 0 0% 98%;
    
    --popover: 240 10% 6%;
    --popover-foreground: 0 0% 98%;
    
    /* Brighter primary for dark mode */
    --primary: 263 70% 65%;
    --primary-foreground: 240 10% 3.9%;
    --primary-glow: 263 70% 80%;
    
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 263 70% 65%;
    
    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(263 70% 65%), hsl(280 70% 70%));
    --gradient-secondary: linear-gradient(135deg, hsl(240 3.7% 15.9%), hsl(240 3.7% 20%));
    --gradient-ai: linear-gradient(135deg, hsl(263 70% 65%), hsl(280 70% 70%), hsl(300 70% 75%));
    
    /* Enhanced shadows for dark mode */
    --shadow-ai: 0 10px 40px -10px hsl(263 70% 65% / 0.4);
    --shadow-card: 0 4px 20px -4px hsl(0 0% 0% / 0.3);
    --shadow-glow: 0 0 50px hsl(263 70% 80% / 0.5);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
  
  /* AI-themed animations */
  .ai-glow {
    box-shadow: var(--shadow-ai);
  }
  
  .ai-gradient {
    background: var(--gradient-ai);
  }
  
  .ai-transition {
    transition: var(--transition-ai);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Mobile-responsive utilities */
@layer utilities {
  /* Mobile-first responsive text sizes */
  .text-responsive-xs { @apply text-xs sm:text-sm; }
  .text-responsive-sm { @apply text-sm sm:text-base; }
  .text-responsive-base { @apply text-base sm:text-lg; }
  .text-responsive-lg { @apply text-lg sm:text-xl; }
  .text-responsive-xl { @apply text-xl sm:text-2xl; }
  .text-responsive-2xl { @apply text-2xl sm:text-3xl; }
  .text-responsive-3xl { @apply text-3xl sm:text-4xl; }

  /* Mobile-friendly spacing */
  .p-responsive { @apply p-4 sm:p-6 lg:p-8; }
  .px-responsive { @apply px-4 sm:px-6 lg:px-8; }
  .py-responsive { @apply py-4 sm:py-6 lg:py-8; }
  .m-responsive { @apply m-4 sm:m-6 lg:m-8; }
  .mx-responsive { @apply mx-4 sm:mx-6 lg:mx-8; }
  .my-responsive { @apply my-4 sm:my-6 lg:my-8; }

  /* Mobile-friendly gaps */
  .gap-responsive { @apply gap-4 sm:gap-6 lg:gap-8; }
  .space-y-responsive > * + * { @apply mt-4 sm:mt-6 lg:mt-8; }
  .space-x-responsive > * + * { @apply ml-4 sm:ml-6 lg:ml-8; }

  /* Mobile-friendly grid layouts */
  .grid-responsive-1 { @apply grid grid-cols-1; }
  .grid-responsive-2 { @apply grid grid-cols-1 sm:grid-cols-2; }
  .grid-responsive-3 { @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3; }
  .grid-responsive-4 { @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4; }
  .grid-responsive-auto { @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4; }

  /* Mobile-friendly containers */
  .container-responsive { @apply container mx-auto px-4 sm:px-6 lg:px-8; }

  /* Touch-friendly interactive elements */
  .touch-target { @apply min-h-[44px] min-w-[44px]; }
  .touch-button { @apply px-4 py-2 sm:px-6 sm:py-3 touch-target; }

  /* Mobile-optimized shadows */
  .shadow-mobile { @apply shadow-sm sm:shadow-md lg:shadow-lg; }

  /* Safe area handling for mobile devices */
  .safe-top { padding-top: env(safe-area-inset-top); }
  .safe-bottom { padding-bottom: env(safe-area-inset-bottom); }
  .safe-left { padding-left: env(safe-area-inset-left); }
  .safe-right { padding-right: env(safe-area-inset-right); }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}