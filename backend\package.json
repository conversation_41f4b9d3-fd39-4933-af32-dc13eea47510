{"name": "promptify-backend", "version": "1.0.0", "description": "Backend API for Promptify - AI Prompt Creation Platform", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:check": "node scripts/startup-check.js && nodemon server.js", "dev:clean": "node scripts/port-manager.js kill 8001 && npm run dev", "validate": "node scripts/startup-check.js", "port:check": "node scripts/port-manager.js check 8001", "port:kill": "node scripts/port-manager.js kill 8001", "port:kill-all": "node scripts/port-manager.js kill-all-node", "port:find": "node scripts/port-manager.js find 8000", "build": "echo 'No build step required'", "test": "echo 'No tests specified'"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "razorpay": "^2.9.2", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["ai", "prompts", "api", "express", "mongodb"], "author": "Promptify Team", "license": "MIT"}